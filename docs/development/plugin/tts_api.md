# TTS Plugin API 结构

## TTS Plugin 端点

TTS Plugin 提供以下端点：

### 1. POST `/synthesize`
合成语音数据，使用已存在的插件配置实例。

**请求体：**
```json
{
    "text": "要合成的文本",
    "plugin_config": {
        "api_key": "your_fish_audio_api_key",
        "reference_id": "voice_reference_id",
        "model": "speech-1.6",
        "format": "mp3",
        "mp3_bitrate": 128,
        "chunk_length": 200,
        "normalize": true,
        "latency": "balanced",
        "temperature": 0.7,
        "top_p": 0.7,
        "prosody": {
            "speed": 1.0,
            "volume": 0
        }
    },
    "custom": {
        "reference_id": "override_reference_id",
        "latency": "normal"
    }
}
```

**响应：**
```json
{
    "audio": "base64_encoded_audio_data",
    "processing_time": 0.123,
    "instance_id": "abc123def456"
}
```

**错误处理：**
- 如果 `plugin_config` 对应的实例不存在，返回 404 错误
- 不会自动创建新实例

### 2. WebSocket `/synthesize_stream`
流式语音合成，支持实时文本输入和音频输出。

**连接流程：**

1. **建立连接**
   ```
   ws://localhost:8000/synthesize_stream
   ```

2. **发送初始配置**
   ```json
   {
       "plugin_config": {
           "api_key": "your_fish_audio_api_key",
           "reference_id": "voice_reference_id",
           "format": "mp3",
           "latency": "balanced"
       },
       "custom": {
           "temperature": 0.8
       }
   }
   ```

3. **接收就绪信号**
   ```json
   {
       "status": "ready",
       "instance_id": "abc123def456"
   }
   ```

4. **发送文本块**
   ```json
   {
       "type": "text",
       "text": "Hello "
   }
   ```

5. **接收音频块**
   ```json
   {
       "type": "audio",
       "audio": "base64_encoded_audio_chunk"
   }
   ```

6. **发送结束信号**
   ```json
   {
       "type": "end"
   }
   ```

7. **接收完成信号**
   ```json
   {
       "type": "complete"
   }
   ```

**错误处理：**
- 如果配置无效，连接会被关闭并返回错误信息
- 如果引擎不支持流式合成，会返回相应错误

### 3. GET `/health`
检查 TTS 服务健康状态。

**响应：**
```json
{
    "status": "healthy"  // healthy, partial, not_ready, no_instances
}
```

### 4. POST `/create_instance` (内部端点)
创建新的引擎实例（由 launcher 调用）。

**请求体：**
```json
{
    "config": {
        "api_key": "your_api_key",
        "reference_id": "voice_id",
        "model": "speech-1.6"
    }
}
```

**响应：**
```json
{
    "message": "Instance created successfully",
    "instance_id": "abc123def456",
    "config": {...}
}
```

### 5. DELETE `/instances/{instance_id}` (内部端点)
删除引擎实例（由 launcher 调用）。

**响应：**
```json
{
    "message": "Instance abc123def456 deleted successfully"
}
```

### 6. GET `/instances` (内部端点)
列出所有引擎实例（由 launcher 调用）。

**响应：**
```json
{
    "instances": {
        "abc123def456": {
            "ready": true,
            "supports_streaming": true,
            "config": {...}
        }
    }
}
```

### 7. GET `/plugin-config`
获取插件配置信息（用于远程插件发现）。

**响应：**
```json
{
    "name": "fish_audio_tts_plugin",
    "version": "1.0.0",
    "description": "Fish Audio TTS Plugin",
    "service_type": "tts",
    "plugin_json_schema": {...},
    "plugin_ui_schema": {...}
}
```

## 支持的音频格式

- **MP3**: 默认格式，支持 64/128/192 kbps
- **WAV**: 无损格式
- **PCM**: 原始音频数据
- **Opus**: 低延迟格式，适合流式应用

## 延迟模式

- **normal**: 标准延迟，更高的稳定性
- **balanced**: 平衡模式，约 300ms 延迟，可能降低稳定性

## 自定义参数

`custom` 字段支持以下参数：

- `reference_id`: 覆盖默认的语音参考 ID
- `latency`: 覆盖延迟模式
- `temperature`: 控制语音生成的随机性 (0.0-2.0)
- `top_p`: 控制多样性 (0.0-1.0)

## 错误码

- **400**: 请求参数错误
- **404**: 实例不存在
- **500**: 服务器内部错误
- **503**: 服务不可用（引擎未就绪）

## 使用注意事项

1. **实例管理**: 插件使用配置哈希来管理实例，相同配置会复用实例
2. **流式合成**: WebSocket 连接支持实时文本输入和音频输出
3. **音频编码**: 所有音频数据都使用 base64 编码传输
4. **连接管理**: WebSocket 连接会自动处理异常和清理
